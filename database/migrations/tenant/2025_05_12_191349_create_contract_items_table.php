<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contract_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('contract_id')->constrained();
            $table->foreignId('equipment_id')->constrained();
            $table->foreignId('service_type_id')->nullable()->constrained();
            $table->integer('quantity')->default(1);
            $table->integer('interval')->nullable();
            $table->string('period')->nullable();
            $table->date('started_at');
            $table->date('ended_at')->nullable();
            $table->boolean('generate_service_order_for_starting_date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contract_items');
    }
};
