<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('checklist_steps', function (Blueprint $table) {
            $table->string('boolean_table_negative_value_name')
                ->nullable()
                ->after('boolean_table_positive_value_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('checklist_steps', function (Blueprint $table) {
            $table->dropColumn('boolean_table_negative_value_name');
        });
    }
};
