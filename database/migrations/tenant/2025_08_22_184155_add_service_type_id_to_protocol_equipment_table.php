<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('protocol_equipment', function (Blueprint $table) {
            $table->foreignId('service_type_id')
                ->nullable()
                ->after('equipment_id')
                ->constrained();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('protocol_equipment', function (Blueprint $table) {
            $table->dropConstrainedForeignId('service_type_id');
        });
    }
};
