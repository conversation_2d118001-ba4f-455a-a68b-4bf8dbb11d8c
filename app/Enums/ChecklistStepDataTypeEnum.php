<?php

namespace App\Enums;

enum ChecklistStepDataTypeEnum: string
{
    case BooleanTable = 'boolean-table';
    case DescriptionTable = 'description-table';
    case Character = 'character';
    case Date = 'date';
    case Image = 'image';
    case MultipleChoice = 'multiple-choice';
    case Number = 'number';
    case ProgressReport = 'progress-report';
    case Signature = 'signature';
    case SingleChoice = 'single-choice';
    case Text = 'text';

    public static function getTranslated(): array
    {
        return [
            self::Signature->value => 'Assinatura',
            self::Character->value => 'Caractere',
            self::Date->value => 'Data',
            self::SingleChoice->value => 'Escolha simples',
            self::Image->value => 'Imagem',
            self::ProgressReport->value => 'Medição',
            self::MultipleChoice->value => 'Múltipla escolha',
            self::Number->value => 'Número',
            self::DescriptionTable->value => 'Tabela descritiva',
            self::BooleanTable->value => 'Tabela sim/não',
            self::Text->value => 'Texto',
        ];
    }
}
