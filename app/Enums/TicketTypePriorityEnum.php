<?php

namespace App\Enums;

enum TicketTypePriorityEnum: string
{
    case Normal = 'normal';
    case High = 'high';
    case Urgent = 'urgent';

    public static function getTranslated(): array
    {
        return [
            self::Normal->value => 'Normal',
            self::High->value => 'Alta',
            self::Urgent->value => 'Urgente',
        ];
    }

    public static function getTableColors(): array
    {
        return [
            self::Normal->value => 'gray',
            self::High->value => 'warning',
            self::Urgent->value => 'danger',
        ];
    }
}