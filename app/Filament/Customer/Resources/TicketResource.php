<?php

namespace App\Filament\Customer\Resources;

use App\Actions\Equipment\Queries\GetEquipmentBySerialNumberOrName;
use App\Actions\TicketType\GetTicketTypes;
use App\Core\Filament\Filters\TableSelectFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\ServiceOrderStatusEnum;
use App\Enums\TicketStatusEnum;
use App\Filament\Customer\Resources\TicketResource\Pages;
use App\Models\Equipment;
use App\Models\ServiceOrder;
use App\Models\Ticket;
use App\Models\TicketReply;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;
use Throwable;

class TicketResource extends Resource
{
    protected static ?string $model = Ticket::class;
    protected static ?string $modelLabel = 'chamado';
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Tabs::make()->schema([
                    Tab::make('Geral')->schema([
                        Grid::make(2)->schema([
                            Select::make('ticket_type_id')
                                ->label(__('tickets.forms.fields.ticket_type_id'))
                                ->relationship('ticketType', 'name')
                                ->required(),
                            Select::make('equipment_id')
                                ->label(__('tickets.forms.fields.equipment_id'))
                                ->required()
                                ->searchable()
                                ->lazy()
                                ->getSearchResultsUsing(fn(string $search): array => GetEquipmentBySerialNumberOrName::run($search)->pluck('name', 'id')->toArray())
                                ->getOptionLabelUsing(fn(?string $state, ?Ticket $ticket): ?string => $ticket->equipment->name ?? Equipment::find($state)->name ?? ''),
                        ]),
                        Grid::make(1)->schema([
                            TextInput::make('summary')
                                ->label(__('tickets.forms.fields.summary'))
                                ->required(),
                        ]),
                        Grid::make(1)->schema([
                            Textarea::make('description')
                                ->label(__('tickets.forms.fields.description'))
                                ->required()
                                ->rows(10),
                        ]),
                        Grid::make(1)->schema([
                            TableRepeater::make('ticket_files')
                                ->label('Anexos')
                                ->relationship('ticketFiles')
                                ->hiddenLabel()
                                ->defaultItems(0)
                                ->addActionLabel('Adicionar anexo')
                                ->headers([
                                    Header::make('Arquivo'),
                                ])
                                ->schema([
                                    FileUpload::make('path')
                                        ->label('Arquivo')
                                        ->disk('digitalocean')
                                        ->directory(tenant('id') . '/tickets')
                                        ->downloadable(),
                                    Hidden::make('provider')
                                        ->default('digitalocean'),
                                ]),
                        ]),
                    ]),
                    Tab::make('Ordens de serviço')
                        ->hiddenOn('create')
                        ->schema([
                            Grid::make(1)->schema([
                                TableRepeater::make('service_orders')
                                    ->relationship('serviceOrders')
                                    ->hiddenLabel()
                                    ->defaultItems(0)
                                    ->addable(false)
                                    ->deletable(false)
                                    ->reorderable()
                                    ->headers([
                                        Header::make('Código'),
                                        Header::make('Criada em'),
                                        Header::make('Status'),
                                    ])
                                    ->schema([
                                        TextInput::make('code'),
                                        TextInput::make('created_at')
                                            ->disabled()
                                            ->formatStateUsing(fn(?ServiceOrder $serviceOrder): string => format_datetime($serviceOrder->created_at)),
                                        TextInput::make('status')
                                            ->disabled()
                                            ->formatStateUsing(fn(?ServiceOrder $serviceOrder): string => ServiceOrderStatusEnum::getTranslated()[$serviceOrder->status]),
                                    ]),
                            ]),
                        ]),
                ]),
            ]),
            Grid::make(1)->schema([
                Section::make('Respostas')
                    ->schema([
                        Placeholder::make('ticket_replies')
                            ->hiddenLabel()
                            ->content(function (Ticket $ticket): HtmlString {
                                return new HtmlString(
                                    $ticket->ticketReplies()
                                        ->where('public', true)
                                        ->latest()
                                        ->get()
                                        ->map(function (TicketReply $ticketReply): string {
                                            return new HtmlString(
                                                '<span class="text-gray-500">' . format_datetime($ticketReply->created_at) . '</span><br><span class="font-bold">' . $ticketReply->user->name . '</span> respondeu o chamado com as seguintes informações:<br>'
                                                    . $ticketReply->message
                                            );
                                        })
                                        ->implode('<hr class="my-4">')
                                );
                            }),
                    ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label(__('tickets.forms.fields.id')),
                TextColumn::make('createdByUser.name')
                    ->label(__('tickets.forms.fields.created_by_user_id')),
                TextColumn::make('equipment.name')
                    ->label(__('tickets.forms.fields.equipment_id')),
                TextColumn::make('summary')
                    ->label(__('tickets.forms.fields.summary')),
                TextColumn::make('ticketType.name')
                    ->label(__('tickets.forms.fields.ticket_type_id')),
                TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(fn(Ticket $ticket): string => TicketStatusEnum::getTranslated()[$ticket->status])
                    ->color(fn(Ticket $ticket): string => TicketStatusEnum::getTableColors()[$ticket->status]),
                TextColumn::make('created_at')
                    ->label(__('tickets.forms.fields.created_at'))
                    ->formatStateUsing(fn(Ticket $ticket): string => format_datetime($ticket->created_at)),
            ])
            ->filters([
                TableTextFilter::buildLike('tickets', 'id'),
                TableTextFilter::buildRelation('tickets', 'created_by_user_id', 'createdByUser', 'name'),
                TableTextFilter::buildRelation('tickets', 'equipment_id', 'equipment', 'name'),
                TableTextFilter::buildLike('tickets', 'summary'),
                TableSelectFilter::build('tickets', 'ticket_type_id', GetTicketTypes::run(true)->pluck('name', 'id')->toArray()),
                TableSelectFilter::build('tickets', 'status', TicketStatusEnum::getTranslated()),
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge),
                    DeleteAction::make(),
                ]),
            ])
            ->bulkActions([])
            ->emptyStateHeading('Ainda sem chamados')
            ->emptyStateDescription('Assim que você cadastrar seus chamados, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTickets::route('/'),
            'create' => Pages\CreateTicket::route('/create'),
            'view' => Pages\ViewTicket::route('/{record}'),
        ];
    }
}
