<?php

namespace App\Filament\Customer\Resources\TicketResource\Pages;

use App\Actions\TicketReply\CreateTicketReply;
use App\Filament\Customer\Resources\TicketResource;
use Filament\Actions;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Textarea;
use Filament\Resources\Pages\ViewRecord;
use Throwable;

class ViewTicket extends ViewRecord
{
    protected static string $resource = TicketResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('answer')
                ->label('Responder')
                ->icon('heroicon-o-chat-bubble-left-right')
                ->color('gray')
                ->form([
                    Grid::make(1)->schema([
                        Textarea::make('message')
                            ->label('Resposta')
                            ->required()
                            ->rows(10),
                    ]),
                ])
                ->action(function (array $data): void {
                    try {
                        CreateTicketReply::run($this->record, $data);
                        success_notification(__('tickets.responses.answer.success'))->send();
                    } catch (Throwable $th) {
                        error_notification($th->getMessage())->send();
                    }
                }),
        ];
    }
}
