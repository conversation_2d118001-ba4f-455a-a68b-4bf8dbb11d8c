<?php

namespace App\Filament\Customer\Resources;

use App\Actions\ServiceType\Queries\GetServiceTypes;
use App\Core\Filament\Filters\TableSelectFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\ServiceOrderStatusEnum;
use App\Filament\Customer\Resources\ServiceOrderResource\Pages;
use App\Models\ServiceOrder;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ServiceOrderResource extends Resource
{
    protected static ?string $model = ServiceOrder::class;
    protected static ?string $modelLabel = 'ordem de serviço';
    protected static ?string $pluralModelLabel = 'ordens de serviço';
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
    protected static ?int $navigationSort = 1;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('customer_id', Auth::user()->customer->id)
            ->with([
                'customer',
                'equipment',
                'serviceType',
                'employee',
                'protocol',
                'ticket',
                'contractItem',
                'serviceOrderChecklists.checklist',
                'serviceOrderExecutionChecklistSteps'
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist->schema([
            Section::make('Informações Básicas')
                ->schema([
                    Grid::make(4)->schema([
                        TextEntry::make('code')
                            ->label('Código'),
                        TextEntry::make('protocol.code')
                            ->label('Protocolo')
                            ->default('N/A'),
                        TextEntry::make('ticket.id')
                            ->label('Chamado')
                            ->default('N/A'),
                        TextEntry::make('friendly_status')
                            ->label('Status')
                            ->badge()
                            ->color(fn(ServiceOrder $record): string => ServiceOrderStatusEnum::getTableColors()[$record->status] ?? 'gray'),
                    ]),
                    Grid::make(3)->schema([
                        TextEntry::make('customer.name')
                            ->label('Cliente'),
                        TextEntry::make('equipment.name')
                            ->label('Equipamento'),
                        TextEntry::make('serviceType.name')
                            ->label('Tipo de Serviço'),
                    ]),
                    Grid::make(2)->schema([
                        TextEntry::make('employee.name')
                            ->label('Funcionário')
                            ->default('N/A'),
                        TextEntry::make('quantity')
                            ->label('Quantidade')
                            ->numeric(),
                    ]),
                ]),
            Section::make('Descrição e Informações Adicionais')
                ->schema([
                    TextEntry::make('description')
                        ->label('Descrição')
                        ->columnSpanFull()
                        ->default('N/A'),
                    TextEntry::make('visit_additional_info')
                        ->label('Informações Adicionais da Visita')
                        ->columnSpanFull()
                        ->default('N/A'),
                ]),
            Section::make('Datas e Duração')
                ->schema([
                    Grid::make(3)->schema([
                        TextEntry::make('scheduled_to')
                            ->label('Agendado para')
                            ->dateTime('d/m/Y H:i'),
                        TextEntry::make('started_at')
                            ->label('Iniciado em')
                            ->dateTime('d/m/Y H:i'),
                        TextEntry::make('finished_at')
                            ->label('Finalizado em')
                            ->dateTime('d/m/Y H:i'),
                    ]),
                    Grid::make(2)->schema([
                        TextEntry::make('estimated_duration_in_minutes')
                            ->label('Duração Estimada (minutos)')
                            ->numeric()
                            ->default('N/A'),
                        TextEntry::make('created_at')
                            ->label('Criado em')
                            ->dateTime('d/m/Y H:i'),
                    ]),
                ]),
            Section::make('Cancelamento')
                ->schema([
                    Grid::make(2)->schema([
                        TextEntry::make('cancelled_at')
                            ->label('Cancelado em')
                            ->dateTime('d/m/Y H:i'),
                        TextEntry::make('cancellation_additional_info')
                            ->label('Informações do Cancelamento')
                            ->default('N/A'),
                    ]),
                ])
                ->visible(fn(ServiceOrder $record): bool => $record->status === ServiceOrderStatusEnum::Cancelled->value),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('code')
                    ->label('Código')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('equipment.name')
                    ->label('Equipamento')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('serviceType.name')
                    ->label('Tipo de Serviço')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('scheduled_to')
                    ->label('Agendado para')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                TextColumn::make('friendly_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(ServiceOrder $record): string => ServiceOrderStatusEnum::getTableColors()[$record->status] ?? 'gray')
                    ->sortable('status'),
                TextColumn::make('created_at')
                    ->label('Criado em')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                TableTextFilter::buildLike('service_orders', 'code'),
                TableTextFilter::buildRelation('service_orders', 'equipment_id', 'equipment', 'name'),
                TableSelectFilter::build('service_orders', 'service_type_id', GetServiceTypes::run()->pluck('name', 'id')->toArray()),
                TableSelectFilter::build('service_orders', 'status', ServiceOrderStatusEnum::getTranslated()),
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge),
                ]),
            ])
            ->emptyStateHeading('Ainda sem ordens de serviço')
            ->emptyStateDescription('Assim que suas ordens de serviço forem criadas, elas aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageServiceOrders::route('/'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function canDelete($record): bool
    {
        return false;
    }
}
