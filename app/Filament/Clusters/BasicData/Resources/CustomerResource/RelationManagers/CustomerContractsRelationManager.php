<?php

namespace App\Filament\Clusters\BasicData\Resources\CustomerResource\RelationManagers;

use App\Actions\Checklist\Queries\GetActiveChecklistsByName;
use App\Actions\Equipment\Queries\GetEquipmentBySerialNumberOrName;
use App\Actions\ServiceType\Queries\GetServiceTypeByName;
use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\ContractItemPeriodEnum;
use App\Enums\ContractStatusEnum;
use App\Enums\ContractTypeEnum;
use App\Models\Contract;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class CustomerContractsRelationManager extends RelationManager
{
    protected static string $relationship = 'contracts';
    protected static ?string $title = 'Contratos';
    protected static ?string $modelLabel = 'contrato';
    protected static ?string $pluralModelLabel = 'contratos';

    public function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Tabs::make()->schema([
                    Tab::make('Geral')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('code')
                                ->label(__('contracts.forms.fields.code'))
                                ->required(),
                            Select::make('type')
                                ->label(__('contracts.forms.fields.type'))
                                ->required()
                                ->options(ContractTypeEnum::getTranslated()),
                        ]),
                        Grid::make(4)->schema([
                            DatePicker::make('started_at')
                                ->label(__('contracts.forms.fields.started_at'))
                                ->required(),
                            DatePicker::make('ended_at')
                                ->label(__('contracts.forms.fields.ended_at')),
                            DatePicker::make('signed_at')
                                ->label(__('contracts.forms.fields.signed_at')),
                        ]),
                    ]),
                    Tab::make('Itens')->schema([
                        Grid::make(1)->schema([
                            Repeater::make('contract_items')
                                ->hiddenLabel()
                                ->relationship('contractItems')
                                ->addActionLabel('Adicionar item')
                                ->collapsible()
                                ->schema([
                                    Grid::make(4)->schema([
                                        Select::make('equipment_id')
                                            ->label(__('contract_items.forms.fields.equipment_id'))
                                            ->columnSpan(2)
                                            ->relationship('equipment', 'name')
                                            ->required()
                                            ->searchable()
                                            ->lazy()
                                            ->getSearchResultsUsing(fn(string $search): array => GetEquipmentBySerialNumberOrName::run($search)->pluck('name', 'id')->toArray()),
                                        Select::make('service_type_id')
                                            ->label(__('contract_items.forms.fields.service_type_id'))
                                            ->relationship('serviceType', 'name')
                                            ->required()
                                            ->searchable()
                                            ->lazy()
                                            ->getSearchResultsUsing(fn(string $search): array => GetServiceTypeByName::run($search)->pluck('name', 'id')->toArray()),
                                        Select::make('checklist_id')
                                            ->label(__('contract_items.forms.fields.checklist_id'))
                                            ->relationship('checklist', 'name')
                                            ->required()
                                            ->searchable()
                                            ->lazy()
                                            ->getSearchResultsUsing(fn(string $search): array => GetActiveChecklistsByName::run($search)->pluck('name', 'id')->toArray()),
                                    ]),
                                    Grid::make(4)->schema([
                                        TextInput::make('interval')
                                            ->label(__('contract_items.forms.fields.interval'))
                                            ->required(),
                                        Select::make('period')
                                            ->label(__('contract_items.forms.fields.period'))
                                            ->required()
                                            ->options(ContractItemPeriodEnum::getTranslated()),
                                        DatePicker::make('started_at')
                                            ->label(__('contract_items.forms.fields.started_at'))
                                            ->required(),
                                        DatePicker::make('ended_at')
                                            ->label(__('contract_items.forms.fields.ended_at'))
                                            ->required(),
                                    ]),
                                    Grid::make(1)->schema([
                                        Textarea::make('additional_info')
                                            ->label(__('contract_items.forms.fields.additional_info'))
                                            ->rows(3),
                                    ]),
                                    Grid::make(1)->schema([
                                        Toggle::make('generate_service_order_for_starting_date')
                                            ->label(__('contract_items.forms.fields.generate_service_order_for_starting_date'))
                                            ->default(false),
                                    ])
                                ]),
                        ]),
                    ]),
                ]),
            ]),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('contract.code')
            ->columns([
                TextColumn::make('code')
                    ->label(__('contracts.forms.fields.code'))
                    ->sortable(),
                TextColumn::make('type')
                    ->label(__('contracts.forms.fields.type'))
                    ->sortable()
                    ->formatStateUsing(fn(Contract $contract): string => $contract->friendly_type),
                TextColumn::make('started_at')
                    ->label(__('contracts.forms.fields.started_at'))
                    ->sortable()
                    ->formatStateUsing(fn(Contract $contract): string => format_date($contract->started_at)),
                TextColumn::make('ended_at')
                    ->label(__('contracts.forms.fields.ended_at'))
                    ->sortable()
                    ->formatStateUsing(fn(Contract $contract): string => format_date($contract->ended_at)),
                TextColumn::make('created_at')
                    ->label(__('contracts.forms.fields.created_at'))
                    ->sortable()
                    ->formatStateUsing(fn(Contract $contract): string => format_date($contract->created_at)),
                TextColumn::make('status')
                    ->label(__('contracts.forms.fields.status'))
                    ->sortable()
                    ->formatStateUsing(fn(Contract $contract): string => $contract->friendly_status)
                    ->badge()
                    ->color(fn(Contract $contract): string => ContractStatusEnum::getTableColors()[$contract->status]),
            ])
            ->filters([
                TableTextFilter::buildLike('contracts', 'code'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->modalWidth(MaxWidth::Full),
                ]),
            ]);
    }
}
