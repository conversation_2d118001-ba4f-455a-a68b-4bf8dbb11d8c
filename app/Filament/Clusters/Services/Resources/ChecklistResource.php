<?php

namespace App\Filament\Clusters\Services\Resources;

use App\Actions\Checklist\DeleteChecklist;
use App\Actions\Checklist\EditChecklist;
use App\Core\Filament\Filters\TableActiveFilter;
use App\Core\Filament\Filters\TableSelectFilter;
use App\Core\Filament\Filters\TableTextFilter;
use App\Enums\ChecklistStepDataTypeEnum;
use App\Enums\ChecklistTypeEnum;
use App\Enums\PdfOrientationEnum;
use App\Filament\Clusters\Services;
use App\Filament\Clusters\Services\Resources\ChecklistResource\Pages;
use App\Models\Checklist;
use App\Models\ChecklistStep;
use App\Models\ChecklistStepOption;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Dotswan\FilamentCodeEditor\Fields\CodeEditor;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Throwable;

class ChecklistResource extends Resource
{
    protected static ?string $model = Checklist::class;
    protected static ?string $modelLabel = 'roteiro';
    protected static ?string $navigationIcon = 'heroicon-o-queue-list';
    protected static ?string $cluster = Services::class;
    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(1)->schema([
                Toggle::make('active')
                    ->label(__('checklists.forms.fields.active'))
                    ->default(true),
            ]),
            Grid::make(1)->schema([
                Tabs::make()->schema([
                    Tab::make('Geral')->schema([
                        Grid::make(4)->schema([
                            TextInput::make('name')
                                ->label(__('checklists.forms.fields.name'))
                                ->required()
                                ->columnSpan(3),
                            Select::make('type')
                                ->label(__('checklists.forms.fields.type'))
                                ->required()
                                ->options(ChecklistTypeEnum::getTranslated()),
                        ]),
                        Grid::make(2)->schema([
                            Toggle::make('shows_in_quote')
                                ->label(__('checklists.forms.fields.shows_in_quote'))
                                ->default(false),
                            Toggle::make('sends_email_on_service_order_conclusion')
                                ->label(__('checklists.forms.fields.sends_email_on_service_order_conclusion'))
                                ->default(false)
                                ->reactive(),
                        ]),
                        Grid::make(1)
                            ->visible(fn(Get $get): bool => $get('sends_email_on_service_order_conclusion'))
                            ->schema([
                                TextInput::make('conclusion_email_addresses')
                                    ->label(__('checklists.forms.fields.conclusion_email_addresses'))
                                    ->maxLength(1000)
                                    ->placeholder('<EMAIL>,<EMAIL>'),
                            ]),
                    ]),
                    Tab::make('Passos')->schema([
                        Grid::make(1)->schema([
                            Repeater::make('checklist_steps')
                                ->hiddenLabel()
                                ->addActionLabel('Adicionar passo')
                                ->defaultItems(1)
                                ->collapsible()
                                ->collapsed()
                                ->itemLabel(fn(array $state): ?string => is_null($state['sequence']) || is_null($state['name']) || is_null($state['data_type']) ? '' : ($state['sequence'] . '. ' . $state['name'] . ' (' . ChecklistStepDataTypeEnum::getTranslated()[$state['data_type']] . ')'))
                                ->reactive()
                                ->schema([
                                    Grid::make(4)->schema([
                                        TextInput::make('sequence')
                                            ->label(__('checklist_steps.forms.fields.sequence'))
                                            ->required()
                                            ->lazy()
                                            ->numeric(),
                                        TextInput::make('name')
                                            ->label(__('checklist_steps.forms.fields.name'))
                                            ->required()
                                            ->lazy()
                                            ->columnSpan(2),
                                        Select::make('data_type')
                                            ->label(__('checklist_steps.forms.fields.data_type'))
                                            ->required()
                                            ->options(ChecklistStepDataTypeEnum::getTranslated())
                                            ->lazy(),
                                    ]),
                                    Grid::make(2)->schema([
                                        TextInput::make('boolean_table_positive_value_name')
                                            ->label(__('checklist_steps.forms.fields.boolean_table_positive_value_name'))
                                            ->visible(fn(Get $get): bool => $get('data_type') === ChecklistStepDataTypeEnum::BooleanTable->value),
                                        TextInput::make('boolean_table_negative_value_name')
                                            ->label(__('checklist_steps.forms.fields.boolean_table_negative_value_name'))
                                            ->visible(fn(Get $get): bool => $get('data_type') === ChecklistStepDataTypeEnum::BooleanTable->value),
                                    ]),
                                    Grid::make(1)->schema([
                                        Textarea::make('instructions')
                                            ->label(__('checklist_steps.forms.fields.instructions'))
                                            ->rows(3)
                                            ->maxLength(1000),
                                    ]),
                                    Grid::make(4)->schema([
                                        Toggle::make('requires_comment')
                                            ->label(__('checklist_steps.forms.fields.requires_comment'))
                                            ->default(false),
                                        Toggle::make('has_products')
                                            ->label(__('checklist_steps.forms.fields.has_products'))
                                            ->default(true),
                                        Toggle::make('has_needs')
                                            ->label(__('checklist_steps.forms.fields.has_needs'))
                                            ->default(true),
                                        Toggle::make('shows_in_quote')
                                            ->label(__('checklist_steps.forms.fields.shows_in_quote'))
                                            ->default(true),
                                    ]),
                                    Grid::make(1)
                                        ->visible(function (Get $get): bool {
                                            return in_array($get('data_type'), [
                                                ChecklistStepDataTypeEnum::SingleChoice->value,
                                                ChecklistStepDataTypeEnum::MultipleChoice->value,
                                                ChecklistStepDataTypeEnum::BooleanTable->value,
                                                ChecklistStepDataTypeEnum::DescriptionTable->value,
                                            ]);
                                        })
                                        ->schema([
                                            TableRepeater::make('checklist_step_options')
                                                ->hiddenLabel()
                                                ->addActionLabel('Adicionar opção')
                                                ->defaultItems(1)
                                                ->headers([
                                                    Header::make(__('checklist_step_options.forms.fields.sequence'))
                                                        ->markAsRequired()
                                                        ->width('15%'),
                                                    Header::make(__('checklist_step_options.forms.fields.value'))
                                                        ->markAsRequired(),
                                                    Header::make(__('checklist_step_options.forms.fields.requires_comment'))
                                                        ->width('15%'),
                                                ])
                                                ->schema([
                                                    TextInput::make('sequence')
                                                        ->required()
                                                        ->numeric(),
                                                    TextInput::make('value')
                                                        ->required(),
                                                    Toggle::make('requires_comment')
                                                        ->default(false),
                                                ]),
                                        ]),
                                ]),
                        ]),
                    ]),
                    Tab::make('Template de PDF')->schema([
                        Grid::make(1)->schema([
                            Toggle::make('use_report_template')
                                ->label(__('checklists.forms.fields.use_report_template'))
                                ->default(false),
                        ]),
                        Grid::make(1)->schema([
                            Select::make('orientation')
                                ->label('Orientação da folha')
                                ->required()
                                ->options(PdfOrientationEnum::getTranslated())
                                ->default(PdfOrientationEnum::Portrait->value)
                                ->selectablePlaceholder(false),
                        ]),
                        Grid::make(1)->schema([
                            CodeEditor::make('template')
                                ->hiddenLabel()
                                ->columnSpanFull(),
                        ]),
                    ]),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label(__('checklists.forms.fields.name')),
                TextColumn::make('type')
                    ->label(__('checklists.forms.fields.type'))
                    ->formatStateUsing(fn(Checklist $checklist): string => $checklist->friendly_type),
                IconColumn::make('active')
                    ->label(__('checklists.forms.fields.active'))
                    ->boolean(),
            ])
            ->filters([
                TableTextFilter::buildLike('checklists', 'name'),
                TableSelectFilter::build('checklists', 'type', ChecklistTypeEnum::getTranslated()),
                TableActiveFilter::build('checklists'),
            ])
            ->actions([
                ActionGroup::make([
                    ViewAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge)
                        ->mutateRecordDataUsing(function (Checklist $checklist, array $data): array {
                            $checklistReportTemplate = $checklist->checklistReportTemplates()->latest()->first();

                            return array_merge($data, [
                                'checklist_steps' => $checklist->checklistSteps
                                    ->map(fn(ChecklistStep $checklistStep): array => array_merge($checklistStep->attributesToArray(), [
                                        'checklist_step_options' => $checklistStep->checklistStepOptions
                                            ->map(fn(ChecklistStepOption $checklistStepOption): array => $checklistStepOption->attributesToArray())
                                            ->toArray(),
                                    ]))
                                    ->toArray(),
                                'orientation' => $checklistReportTemplate?->orientation ?? PdfOrientationEnum::Portrait->value,
                                'template' => $checklistReportTemplate?->template,
                            ]);
                        }),
                    EditAction::make()
                        ->modalWidth(MaxWidth::SevenExtraLarge)
                        ->mutateRecordDataUsing(function (Checklist $checklist, array $data): array {
                            $checklistReportTemplate = $checklist->checklistReportTemplates()->latest()->first();

                            return array_merge($data, [
                                'checklist_steps' => $checklist->checklistSteps
                                    ->map(fn(ChecklistStep $checklistStep): array => array_merge($checklistStep->attributesToArray(), [
                                        'checklist_step_options' => $checklistStep->checklistStepOptions
                                            ->map(fn(ChecklistStepOption $checklistStepOption): array => $checklistStepOption->attributesToArray())
                                            ->toArray(),
                                    ]))
                                    ->toArray(),
                                'orientation' => $checklistReportTemplate?->orientation ?? PdfOrientationEnum::Portrait->value,
                                'template' => $checklistReportTemplate?->template,
                            ]);
                        })
                        ->using(function (Checklist $checklist, array $data): void {
                            try {
                                EditChecklist::run($checklist, $data);
                            } catch (Throwable $th) {
                                error_notification($th->getMessage());
                            }
                        })
                        ->successNotification(success_notification(__('checklists.responses.update.success'))),
                    DeleteAction::make()
                        ->using(function (Checklist $checklist): void {
                            try {
                                DeleteChecklist::run($checklist);
                            } catch (Throwable $th) {
                                error_notification($th->getMessage());
                            }
                        })
                        ->successNotification(success_notification(__('checklists.responses.delete.success'))),
                ]),
            ])
            ->bulkActions([])
            ->emptyStateHeading('Ainda sem roteiros')
            ->emptyStateDescription('Assim que você cadastrar seus roteiros, eles aparecerão nesta listagem.');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageChecklists::route('/'),
        ];
    }
}
