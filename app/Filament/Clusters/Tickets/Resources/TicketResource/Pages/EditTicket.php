<?php

namespace App\Filament\Clusters\Tickets\Resources\TicketResource\Pages;

use App\Actions\TicketReply\CreateTicketReply;
use App\Filament\Clusters\Tickets\Resources\TicketResource;
use Filament\Actions;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Throwable;

class EditTicket extends EditRecord
{
    protected static string $resource = TicketResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('answer')
                ->label('Responder')
                ->icon('heroicon-o-chat-bubble-left-right')
                ->color('gray')
                ->form([
                    Grid::make(1)->schema([
                        Textarea::make('message')
                            ->label('Resposta')
                            ->required()
                            ->rows(10),
                    ]),
                    Grid::make(1)->schema([
                        Toggle::make('public')
                            ->label('Pública')
                            ->default(true),
                    ]),
                ])
                ->action(function (array $data): void {
                    try {
                        CreateTicketReply::run($this->record, $data);
                        success_notification(__('tickets.responses.answer.success'))->send();
                    } catch (Throwable $th) {
                        error_notification($th->getMessage())->send();
                    }
                }),
        ];
    }

    protected function getSavedNotification(): ?Notification
    {
        return success_notification(__('tickets.responses.update.success'))->send();
    }
}
