<?php

namespace App\Notifications\App\User;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UserCreated extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private string $password,
        private string $accessUrl
    ) {}

    public function via(): array
    {
        return ['mail'];
    }

    public function viaQueues(): array
    {
        return [
            'mail' => 'notifications',
        ];
    }

    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Bem-vindo(a) ao ' . config('app.name') . ' - Dados de acesso')
            ->greeting('Olá, ' . $notifiable->name . '!')
            ->line('Sua conta foi criada com sucesso em nossa plataforma.')
            ->line('Aqui estão suas informações de primeiro acesso:')
            ->line('')
            ->line('**E-mail:** ' . $notifiable->email)
            ->line('**Senha:** ' . $this->password)
            ->line('')
            ->action('Acessar a plataforma', $this->accessUrl)
            ->line('Por motivos de segurança, recomendamos que você altere sua senha após o primeiro acesso.')
            ->line('Se você não solicitou esta conta, entre em contato conosco.')
            ->salutation('Obrigado por utilizar nossos serviços!');
    }
}
