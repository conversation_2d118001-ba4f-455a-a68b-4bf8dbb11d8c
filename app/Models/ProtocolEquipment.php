<?php

namespace App\Models;

use App\Models\Concerns\ProtocolEquipment\HandlesProtocolEquipmentRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Protocol equipment model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $protocol_id
 * @property  int $equipment_id
 * @property  int $service_type_id
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Protocol $protocol
 * @property  \App\Models\Equipment $equipment
 * @property  \App\Models\ServiceType $serviceType
 */
class ProtocolEquipment extends Model
{
    use HandlesProtocolEquipmentRelationships;

    protected $fillable = [
        'protocol_id',
        'equipment_id',
        'service_type_id',
    ];
}
