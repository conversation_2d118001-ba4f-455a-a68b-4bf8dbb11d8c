<?php

namespace App\Models;

use App\Models\Concerns\Customer\HandlesCustomerAttributes;
use App\Models\Concerns\Customer\HandlesCustomerRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Customer model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $user_id
 * @property  string $name
 * @property  string $trading_name
 * @property  string $tax_id_number
 * @property  string $email
 * @property  string $state_registration
 * @property  string $city_registration
 * @property  string $additional_info
 * @property  bool $active
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_tax_id_number
 *
 * @property  \App\Models\User $user
 *
 * @property  \Illuminate\Support\Collection|\App\Models\CustomerAddress[] $customerAddresses
 * @property  \Illuminate\Support\Collection|\App\Models\CustomerContact[] $customerContacts
 * @property  \Illuminate\Support\Collection|\App\Models\Contract[] $contracts
 */
class Customer extends Model
{
    use HandlesCustomerAttributes;
    use HandlesCustomerRelationships;

    protected $fillable = [
        'user_id',
        'name',
        'trading_name',
        'tax_id_number',
        'email',
        'state_registration',
        'city_registration',
        'additional_info',
        'active',
    ];

    protected $appends = [
        'friendly_tax_id_number',
    ];

    protected $casts = [
        'user_id' => 'int',
        'active' => 'bool',
    ];
}
