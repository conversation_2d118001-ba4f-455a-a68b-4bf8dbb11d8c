<?php

namespace App\Models;

use App\Enums\TicketStatusEnum;
use App\Models\Concerns\Ticket\HandlesTicketRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Ticket model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $ticket_type_id
 * @property  int $customer_id
 * @property  int $equipment_id
 * @property  int $created_by_user_id
 * @property  int $assigned_to_user_id
 * @property  string $summary
 * @property  string $description
 * @property  string $status
 * @property  string $priority
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\TicketType $ticketType
 * @property  \App\Models\Customer $customer
 * @property  \App\Models\Equipment $equipment
 * @property  \App\Models\User $createdByUser
 * @property  \App\Models\User $assignedToUser
 *
 * @property  \Illuminate\Support\Collection|\App\Models\TicketFile[] $ticketFiles
 * @property  \Illuminate\Support\Collection|\App\Models\ServiceOrder[] $serviceOrders
 * @property  \Illuminate\Support\Collection|\App\Models\TicketReply[] $ticketReplies
 */
class Ticket extends Model
{
    use HandlesTicketRelationships;

    protected $fillable = [
        'ticket_type_id',
        'customer_id',
        'equipment_id',
        'created_by_user_id',
        'assigned_to_user_id',
        'summary',
        'description',
        'status',
        'priority',
    ];

    public static function booted(): void
    {
        static::creating(function (self $ticket): void {
            $ticket->created_by_user_id ??= auth()->id();
            $ticket->status ??= TicketStatusEnum::Pending->value;
            $ticket->priority = $ticket->ticketType->priority;
        });
    }
}
