<?php

namespace App\Models\Concerns\Customer;

use App\Models\Contract;
use App\Models\CustomerAddress;
use App\Models\CustomerContact;
use App\Models\ThirdPartyCustomer;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesCustomerRelationships
{
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function customerAddresses(): HasMany
    {
        return $this->hasMany(CustomerAddress::class);
    }

    public function customerContacts(): HasMany
    {
        return $this->hasMany(CustomerContact::class);
    }

    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class);
    }

    public function thirdPartyCustomers(): HasMany
    {
        return $this->hasMany(ThirdPartyCustomer::class);
    }
}
