<?php

namespace App\Models\Concerns\ProtocolEquipment;

use App\Models\Equipment;
use App\Models\Protocol;
use App\Models\ServiceType;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesProtocolEquipmentRelationships
{
    public function protocol(): BelongsTo
    {
        return $this->belongsTo(Protocol::class);
    }

    public function equipment(): BelongsTo
    {
        return $this->belongsTo(Equipment::class);
    }

    public function serviceType(): BelongsTo
    {
        return $this->belongsTo(ServiceType::class);
    }
}
