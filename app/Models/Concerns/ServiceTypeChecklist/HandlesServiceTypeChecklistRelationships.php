<?php

namespace App\Models\Concerns\ServiceTypeChecklist;

use App\Models\Checklist;
use App\Models\ServiceType;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesServiceTypeChecklistRelationships
{
    public function serviceType(): BelongsTo
    {
        return $this->belongsTo(ServiceType::class);
    }

    public function checklist(): BelongsTo
    {
        return $this->belongsTo(Checklist::class);
    }
}
