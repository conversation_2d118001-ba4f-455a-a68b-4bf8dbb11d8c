<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Checklist step model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $checklist_id
 * @property  int $sequence
 * @property  string $name
 * @property  string $data_type
 * @property  string $instructions
 * @property  string $boolean_table_positive_value_name
 * @property  string $boolean_table_negative_value_name
 * @property  bool $requires_comment
 * @property  bool $has_products
 * @property  bool $has_needs
 * @property  bool $shows_in_quote
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Checklist $checklist
 *
 * @property  \Illuminate\Support\Collection|\App\Models\ChecklistStepOption[] $checklistStepOption
 */
class ChecklistStep extends Model
{
    protected $fillable = [
        'checklist_id',
        'sequence',
        'name',
        'data_type',
        'instructions',
        'boolean_table_positive_value_name',
        'boolean_table_negative_value_name',
        'requires_comment',
        'has_products',
        'has_needs',
        'shows_in_quote',
    ];

    protected $casts = [
        'checklist_id' => 'int',
        'requires_comment' => 'bool',
        'has_products' => 'bool',
        'has_needs' => 'bool',
        'shows_in_quote' => 'bool',
    ];

    public function checklist(): BelongsTo
    {
        return $this->belongsTo(Checklist::class);
    }

    public function checklistStepOptions(): HasMany
    {
        return $this->hasMany(ChecklistStepOption::class);
    }
}
