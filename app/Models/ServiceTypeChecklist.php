<?php

namespace App\Models;

use App\Models\Concerns\ServiceTypeChecklist\HandlesServiceTypeChecklistRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Service type checklist model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $service_type_id
 * @property  int $checklist_id
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ServiceType $serviceType
 * @property  \App\Models\Checklist $checklist
 */
class ServiceTypeChecklist extends Model
{
    use HandlesServiceTypeChecklistRelationships;

    protected $fillable = [
        'service_type_id',
        'checklist_id',
    ];
}
