<?php

namespace App\Models;

use App\Models\Concerns\TicketReply\HandlesTicketReplyRelationships;
use Illuminate\Database\Eloquent\Model;

/**
 * Ticket reply model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $ticket_id
 * @property  int $user_id
 * @property  string $message
 * @property  bool $public
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Ticket $ticket
 * @property  \App\Models\User $user
 */
class TicketReply extends Model
{
    use HandlesTicketReplyRelationships;

    protected $fillable = [
        'ticket_id',
        'user_id',
        'message',
        'public',
    ];

    public static function booted(): void
    {
        static::creating(function (self $ticketReply): void {
            $ticketReply->user_id ??= auth()->id();
        });
    }
}
