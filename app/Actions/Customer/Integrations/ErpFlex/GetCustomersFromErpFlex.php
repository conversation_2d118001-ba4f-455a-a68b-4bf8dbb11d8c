<?php

namespace App\Actions\Customer\Integrations\ErpFlex;

use App\Actions\Customer\CreateCustomer;
use App\Actions\Customer\EditCustomer;
use App\Actions\ThirdPartyCustomer\CreateThirdPartyCustomer;
use App\Actions\ThirdPartyCustomer\EditThirdPartyCustomer;
use App\Actions\ThirdPartyCustomer\Queries\GetThirdPartyCustomerByThirdPartyId;
use App\Http\Integrations\ErpFlex\Services\ErpFlexCustomerService;
use App\Models\Customer;
use App\Models\IntegrationSetting;
use App\Models\IntegrationType;
use App\Models\ThirdPartyCustomer;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class GetCustomersFromErpFlex
{
    use AsAction;

    private  bool $force;
    private IntegrationSetting $integrationSetting;
    private  ErpFlexCustomerService $erpFlexCustomerService;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.customers.receive'));
    }

    public function handle(
        IntegrationSetting $integrationSetting,
        bool $force = false,
        ?int $userId = null,
        ?int $erpFlexCustomerId = null
    ): void {
        $this->integrationSetting = $integrationSetting;
        $this->force = $force;
        $this->erpFlexCustomerService = new ErpFlexCustomerService();

        if ($erpFlexCustomerId) {
            $this->processSingleCustomer(
                $this->erpFlexCustomerService->getById($erpFlexCustomerId)
            );

            return;
        }

        $this->integrateCustomers();

        if ($userId) {
            success_database_notification($userId, __('customers.responses.get_from_erp_flex.success'), true);
        }
    }

    private function integrateCustomers(): void
    {
        $executionStart = now();

        if ($this->force) {
            $lastIntegrationAt = null;
        } else {
            $lastIntegrationAt = isset($this->integrationSetting->settings['last_customers_received_at'])
                ? carbon($this->integrationSetting->settings['last_customers_received_at'])
                : now()->startOfMillennium();
        }

        $i = 0;

        while (true) {
            $erpFlexApiCustomers = $this->erpFlexCustomerService->get(1000, $i, $lastIntegrationAt);

            foreach ($erpFlexApiCustomers as $erpFlexApiCustomer) {
                try {
                    $this->processSingleCustomer($erpFlexApiCustomer);
                } catch (Throwable $th) {
                    error($th);
                }
            }

            if (count($erpFlexApiCustomers) < 1000) {
                break;
            }

            $i += 1000;
        }

        $settingsData = $this->integrationSetting->settings;
        $settingsData['last_customers_received_at'] = $executionStart;

        $this->integrationSetting->update(['settings' => $settingsData]);
    }

    private function processSingleCustomer(object $erpFlexApiCustomer): void
    {
        $this->createOrUpdateCustomer(
            $this->createOrUpdateThirdPartyCustomer($erpFlexApiCustomer),
            $erpFlexApiCustomer
        );
    }

    public function createOrUpdateThirdPartyCustomer(mixed $erpFlexApiCustomer): ThirdPartyCustomer
    {
        $data = [
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'third_party_id' => $erpFlexApiCustomer->SA1_ID,
            'third_party_db_read_data' => json_decode(json_encode($erpFlexApiCustomer), true),
        ];

        /** @var \App\Models\ThirdPartyCustomer|null $thirdPartyCustomer */
        $thirdPartyCustomer = GetThirdPartyCustomerByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexApiCustomer->SA1_ID);

        return is_null($thirdPartyCustomer)
            ? CreateThirdPartyCustomer::run($data)
            : EditThirdPartyCustomer::run($thirdPartyCustomer, $data);
    }

    private function createOrUpdateCustomer(ThirdPartyCustomer $thirdPartyCustomer, object $erpFlexApiCustomer): Customer
    {
        $data = [
            'name' => $erpFlexApiCustomer->SA1_Desc,
            'email' => $erpFlexApiCustomer->SA1_EMail,
            'trading_name' => $erpFlexApiCustomer->SA1_Fantasia,
            'tax_id_number' => $erpFlexApiCustomer->SA1_CPF,
            'state_registration' => trim($erpFlexApiCustomer->SA1_Inscr),
            'city_registration' => trim($erpFlexApiCustomer->SA1_InscrM),
        ];

        if (is_null($thirdPartyCustomer->customer_id)) {
            /** @var \App\Models\Customer $customer */
            $customer = CreateCustomer::run($data);

            EditThirdPartyCustomer::run($thirdPartyCustomer, ['customer_id' => $customer->id]);

            return $customer;
        }

        return EditCustomer::run($thirdPartyCustomer->customer, $data);
    }
}
