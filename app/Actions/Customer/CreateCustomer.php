<?php

namespace App\Actions\Customer;

use App\Models\Customer;
use Lorisleiva\Actions\Concerns\AsAction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Throwable;

class CreateCustomer
{
    use AsAction;

    public function handle(array $data): Customer
    {
        try {
            return DB::transaction(function () use ($data): Customer {
                /** @var \App\Models\Customer $customer */
                $customer = Customer::create($data);

                $user = $customer->user()->make([
                    'name' => $data['name'],
                    'email' => $data['email'],
                    'password' => Str::random(),
                ]);

                // Mark this user as a customer user for proper notification URL
                $user->setAsCustomerUser();
                $user->save();

                $customer->user()->associate($user);
                $customer->save();

                return $customer;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
