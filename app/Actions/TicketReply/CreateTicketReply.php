<?php

namespace App\Actions\TicketReply;

use App\Models\Ticket;
use App\Models\TicketReply;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class CreateTicketReply
{
    use AsAction;

    public function handle(Ticket $ticket, array $data): TicketReply
    {
        try {
            return $ticket->ticketReplies()->create($data);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
