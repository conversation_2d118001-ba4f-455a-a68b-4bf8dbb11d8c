<?php

namespace App\Actions\Contract\Integrations\ErpFlex;

use App\Actions\Customer\Integrations\ErpFlex\CreateCustomerInErpFlex;
use App\Actions\ErpFlexParameter\Queries\GetErpFlexParameter;
use App\Actions\ThirdPartyContract\Queries\GetThirdPartyContractByContractId;
use App\Actions\ThirdPartyCustomer\Queries\GetThirdPartyCustomerByCustomerId;
use App\Actions\ThirdPartyServiceType\Queries\GetThirdPartyServiceTypeByServiceTypeId;
use App\Http\Integrations\ErpFlex\DataTransferObjects\Contract\ErpFlexCreateContractDto;
use App\Http\Integrations\ErpFlex\DataTransferObjects\Contract\ErpFlexCreateContractItemDto;
use App\Http\Integrations\ErpFlex\Services\ErpFlexContractService;
use App\Models\Contract;
use App\Models\ContractItem;
use App\Models\ErpFlexParameter;
use App\Models\IntegrationType;
use App\Models\ThirdPartyContract;
use Exception;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;

class CreateContractInErpFlex
{
    use AsAction;

    private ErpFlexParameter $erpFlexParameter;

    public function configureJob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.contracts.send'));
    }

    public function handle(Contract $contract, ?int $userId = null, bool $throwWarnings = false): ?Contract
    {
        /** @var \App\Models\ThirdPartyContract $thirdPartyContract */
        $thirdPartyContract = GetThirdPartyContractByContractId::run(IntegrationType::TYPE_ERP_FLEX, $contract->id);

        if ($thirdPartyContract && $thirdPartyContract->third_party_id) {
            if ($userId) {
                if ($throwWarnings) {
                    warning_notification('O contrato já foi criado anteriormente no ERPFlex.')->send();
                } else {
                    warning_database_notification($userId, 'O contrato já foi criado anteriormente no ERPFlex.', true);
                }
            }

            return null;
        }

        $this->erpFlexParameter = GetErpFlexParameter::run();

        /** @var \App\Models\ThirdPartyCustomer|null $thirdPartyCustomer */
        $thirdPartyCustomer = GetThirdPartyCustomerByCustomerId::run(IntegrationType::TYPE_ERP_FLEX, $contract->customer_id);

        if (!$thirdPartyCustomer->third_party_id) {
            CreateCustomerInErpFlex::run($contract->customer);

            /** @var \App\Models\ThirdPartyCustomer|null $thirdPartyCustomer */
            $thirdPartyCustomer = GetThirdPartyCustomerByCustomerId::run(IntegrationType::TYPE_ERP_FLEX, $contract->customer_id);
        }

        if (!$contract->signed_at) {
            throw new Exception('É necessário informar uma data de assinatura para criar um contrato no ERPFlex.');
        }

        $erpFlexCreateContractDto = new ErpFlexCreateContractDto(
            documento: $contract->code,
            id_cliente: $thirdPartyCustomer->third_party_id,
            status: tenant('erp_flex')['api'][config('app.env')]['default_contract_active_status'],
            vigenciade: carbon($contract->started_at)->format('d/m/Y'),
            faturamento: carbon($contract->started_at)->format('d/m/Y'),
            assinatura: carbon($contract->signed_at)->format('d/m/Y'),
            tipo: 'N',
            tipo_geracao: 'F',
            global_individual: 'I',
            item: $contract->contractItems
                ->map(function (ContractItem $contractItem): ErpFlexCreateContractItemDto {
                    /** @var \App\Models\ThirdPartyServiceType|null $thirdPartyServiceType */
                    $thirdPartyServiceType = GetThirdPartyServiceTypeByServiceTypeId::run(IntegrationType::TYPE_ERP_FLEX, $contractItem->service_type_id);

                    return new ErpFlexCreateContractItemDto(
                        id_produto: $thirdPartyServiceType->third_party_id,
                        id_vendedor: 58347,
                        precuni: 0,
                        prcuniexcedente: 0,
                        prcunicomercial: 0,
                        quantidade: $contractItem->quantity,
                        quantidade_comercial: $contractItem->quantity,
                        valitem: 0,
                        vencto: 30,
                        periodicidade: 1,
                        intervalo: 30,
                        parcelas: 1,
                        sittribicms: 300,
                        sittribipi: 54,
                        sittribpis: '07',
                        sittribcofins: '07',
                        id_natureza: $this->erpFlexParameter->contract_item_default_nature_id,
                        status_item: tenant('erp_flex')['api'][config('app.env')]['default_contract_active_status'],
                        assinatura: carbon($contractItem->started_at)->format('d/m/Y'),
                        vigenciade: carbon($contractItem->started_at)->format('d/m/Y'),
                        faturamento: carbon($contractItem->started_at)->format('d/m/Y'),
                        id_cfop: 343
                    );
                })
                ->toArray(),
        );

        /** @var \App\Models\ThirdPartyContract $thirdPartyContract */
        $thirdPartyContract = ThirdPartyContract::create([
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'contract_id' => $contract->id,
        ]);

        $thirdPartyContract->update([
            'third_party_id' => (new ErpFlexContractService())
                ->create($contract, $thirdPartyContract, $erpFlexCreateContractDto)
                ->data->contrato_id,
        ]);

        if ($userId) {
            success_database_notification($userId, __('contracts.responses.create_in_erp_flex.success'), true);
        }

        return $contract;
    }
}
