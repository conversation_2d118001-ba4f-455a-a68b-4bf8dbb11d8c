<?php

namespace App\Actions\Contract\Integrations\ErpFlex;

use App\Actions\Customer\Integrations\ErpFlex\GetCustomersFromErpFlex;
use App\Actions\Equipment\Integrations\ErpFlex\GetEquipmentFromErpFlex;
use App\Actions\ThirdPartyContract\CreateThirdPartyContract;
use App\Actions\ThirdPartyContract\EditThirdPartyContract;
use App\Actions\ThirdPartyContract\Queries\GetThirdPartyContractByThirdPartyId;
use App\Actions\ThirdPartyCustomer\Queries\GetThirdPartyCustomerByThirdPartyId;
use App\Actions\ThirdPartyEquipment\Queries\GetThirdPartyEquipmentByThirdPartyId;
use App\Enums\ContractStatusEnum;
use App\Enums\ContractTypeEnum;
use App\Http\Integrations\ErpFlex\Services\ErpFlexContractService;
use App\Models\Contract;
use App\Models\IntegrationSetting;
use App\Models\IntegrationType;
use App\Models\ThirdPartyContract;
use Lorisleiva\Actions\Concerns\AsAction;
use Lorisleiva\Actions\Decorators\JobDecorator;
use Throwable;

class GetContractsFromErpFlex
{
    use AsAction;

    private bool $force;
    private IntegrationSetting $integrationSetting;
    private ErpFlexContractService $erpFlexContractService;

    public function configurejob(JobDecorator $job): void
    {
        $job->onQueue(config('queue.custom_names.ouess.contracts.receive'));
    }

    public function handle(
        IntegrationSetting $integrationSetting,
        bool $force = false,
        ?int $userId = null,
        ?int $erpFlexContractId = null
    ): void {
        $this->integrationSetting = $integrationSetting;
        $this->force = $force;
        $this->erpFlexContractService = new ErpFlexContractService();

        if ($erpFlexContractId) {
            $singleContract = $this->erpFlexContractService->getById($erpFlexContractId);

            if ($singleContract) {
                $this->processContractWithItems(collect([$singleContract]));
            }

            return;
        }

        $this->integrateContracts();

        if ($userId) {
            success_database_notification($userId, __('contracts.responses.get_from_erp_flex.success'), true);
        }
    }

    private function integrateContracts(): void
    {
        $executionStart = now();

        if ($this->force) {
            $lastIntegrationAt = null;
        } else {
            $lastIntegrationAt = isset($this->integrationSetting->settings['last_contracts_received_at'])
                ? carbon($this->integrationSetting->settings['last_contracts_received_at'])
                : now()->startOfMillennium();
        }

        $i = 0;

        while (true) {
            $erpFlexApiContracts = $this->erpFlexContractService->get(1000, $i, $lastIntegrationAt);

            $groupedContracts = collect($erpFlexApiContracts)->groupBy('SD5_ID');

            foreach ($groupedContracts as $contractRows) {
                try {
                    $this->processContractWithItems($contractRows);
                } catch (Throwable $th) {
                    error($th);
                }
            }

            if (count($erpFlexApiContracts) < 1000) {
                break;
            }

            $i += 1000;
        }

        $settingsData = $this->integrationSetting->settings;
        $settingsData['last_contracts_received_at'] = $executionStart;

        $this->integrationSetting->update(['settings' => $settingsData]);
    }

    private function processContractWithItems($contractRows): void
    {
        $firstRow = $contractRows->first();
        $thirdPartyContract = $this->createOrUpdateThirdPartyContract($firstRow);
        $contract = $this->createOrUpdateContract($thirdPartyContract, $firstRow);

        foreach ($contractRows as $contractRow) {
            $this->syncContractItems($contract, $contractRow);
        }
    }

    private function createOrUpdateThirdPartyContract(object $erpFlexApiContract): ThirdPartyContract
    {
        $data = [
            'integration_type_id' => IntegrationType::TYPE_ERP_FLEX,
            'third_party_id' => $erpFlexApiContract->SD5_ID,
            'third_party_db_read_data' => json_decode(json_encode($erpFlexApiContract), true),
        ];

        /** @var \App\Models\ThirdPartyContract|null $thirdPartyContract */
        $thirdPartyContract = GetThirdPartyContractByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexApiContract->SD5_ID);

        return is_null($thirdPartyContract)
            ? CreateThirdPartyContract::run($data)
            : EditThirdPartyContract::run($thirdPartyContract, $data);
    }

    private function createOrUpdateContract(ThirdPartyContract $thirdPartyContract, object $erpFlexApiContract): Contract
    {
        /** @var \App\Models\ThirdPartyCustomer|null $thirdPartyCustomer */
        $thirdPartyCustomer = GetThirdPartyCustomerByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexApiContract->SD5_IDSA1);

        if (!$thirdPartyCustomer || !$thirdPartyCustomer->customer_id) {
            GetCustomersFromErpFlex::run($this->integrationSetting, false, null, $erpFlexApiContract->SD5_IDSA1);
        }

        $contractData = [
            'customer_id' => $thirdPartyCustomer->customer_id,
            'code' => $erpFlexApiContract->SD5_Doc,
            'type' => ContractTypeEnum::Preventive->value, // Default to preventive, adjust as needed
            'status' => $erpFlexApiContract->SD5_IDSD7 === tenant('erp_flex')['api'][config('app.env')]['default_contract_active_status']
                ? ContractStatusEnum::Active->value
                : ContractStatusEnum::Inactive->value,
            'started_at' => carbon($erpFlexApiContract->SD5_VigenciaDe)->format('Y-m-d'),
            'ended_at' => $erpFlexApiContract->SD5_VigenciaAte
                ? carbon($erpFlexApiContract->SD5_VigenciaAte)->format('Y-m-d')
                : null,
            'signed_at' => $erpFlexApiContract->SD5_Assinatura
                ? carbon($erpFlexApiContract->SD5_Assinatura)->format('Y-m-d')
                : null,
        ];

        /** @var \App\Models\Contract|null $contract */
        $contract = $thirdPartyContract->contract_id
            ? $thirdPartyContract->contract
            : null;

        if ($contract) {
            $contract->update($contractData);
        } else {
            $contract = Contract::create($contractData);
            $thirdPartyContract->update(['contract_id' => $contract->id]);
        }

        return $contract;
    }

    private function syncContractItems(Contract $contract, object $erpFlexApiContract): void
    {
        if (!isset($erpFlexApiContract->SD6_IDSD5) || !$erpFlexApiContract->SD6_IDSB1) {
            return;
        }

        /** @var \App\Models\ThirdPartyEquipment|null $thirdPartyEquipment */
        $thirdPartyEquipment = GetThirdPartyEquipmentByThirdPartyId::run(IntegrationType::TYPE_ERP_FLEX, $erpFlexApiContract->SD6_IDSB1);

        if (!$thirdPartyEquipment || !$thirdPartyEquipment->equipment_id) {
            GetEquipmentFromErpFlex::run($this->integrationSetting, false, null, $erpFlexApiContract->SD6_IDSB1);
        }

        $contractItemData = [
            'contract_id' => $contract->id,
            'equipment_id' => $thirdPartyEquipment->equipment_id,
            'quantity' => (int) ($erpFlexApiContract->SD6_Quant ?? 1),
            'started_at' => $erpFlexApiContract->SD6_VigenciaDe
                ? carbon($erpFlexApiContract->SD6_VigenciaDe)->format('Y-m-d')
                : $contract->started_at,
            'ended_at' => $erpFlexApiContract->SD6_VigenciaAte
                ? carbon($erpFlexApiContract->SD6_VigenciaAte)->format('Y-m-d')
                : $contract->ended_at,
            'generate_service_order_for_starting_date' => false,
        ];

        $existingItem = $contract->contractItems()
            ->where('equipment_id', $contractItemData['equipment_id'])
            ->first();

        if ($existingItem) {
            $existingItem->update($contractItemData);
        } else {
            $contract->contractItems()->create($contractItemData);
        }
    }
}
