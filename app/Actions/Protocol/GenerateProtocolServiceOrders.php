<?php

namespace App\Actions\Protocol;

use App\Actions\ServiceOrder\CreateServiceOrder;
use App\Models\Protocol;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateProtocolServiceOrders
{
    use AsAction;

    public function handle(Protocol $protocol, array $checklistIds): Protocol
    {
        try {
            return DB::transaction(function () use ($protocol, $checklistIds): Protocol {
                for ($i = 1; $i <= $protocol->protocolEquipment->count(); $i++) {
                    $protocol->protocolServiceOrders()->create([
                        'service_order_id' => CreateServiceOrder::run([
                            'code' => $protocol->code . '-' . $i,
                            'protocol_id' => $protocol->id,
                            'customer_id' => $protocol->customer_id,
                            'equipment_id' => $protocol->protocolEquipment[$i - 1]->equipment_id,
                            'service_type_id' => $protocol->protocolEquipment[$i - 1]->service_type_id,
                            'estimated_duration_in_minutes' => $protocol->protocolEquipment[$i - 1]->serviceType->estimated_duration_in_minutes,
                            'checklist_ids' => array_merge(
                                $protocol->protocolEquipment[$i - 1]->serviceType->serviceTypeChecklists->pluck('checklist_id')->toArray(),
                                $checklistIds,
                            ),
                        ])->id
                    ]);
                }

                return $protocol;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
