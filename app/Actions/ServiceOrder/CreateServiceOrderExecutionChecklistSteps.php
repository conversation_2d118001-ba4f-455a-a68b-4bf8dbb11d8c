<?php

namespace App\Actions\ServiceOrder;

use App\Enums\ChecklistStepDataTypeEnum;
use App\Models\ServiceOrder;
use App\Models\ServiceOrderChecklist;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateServiceOrderExecutionChecklistSteps
{
    use AsAction;

    private int $sequence = 1;
    private ServiceOrder $serviceOrder;

    public function handle(ServiceOrder $serviceOrder, ?ServiceOrderChecklist $serviceOrderChecklist = null): ServiceOrder
    {
        $this->serviceOrder = $serviceOrder;

        $this->sequence = $this->serviceOrder->serviceOrderExecutionChecklistSteps()->count() + 1;

        return DB::transaction(function () use ($serviceOrderChecklist) {
            if ($serviceOrderChecklist) {
                $this->createChecklistSteps($serviceOrderChecklist);
                return $this->serviceOrder;
            }

            $this->serviceOrder->serviceOrderChecklists->each(function (ServiceOrderChecklist $serviceOrderChecklist) {
                $this->createChecklistSteps($serviceOrderChecklist);
            });

            return $this->serviceOrder;
        });
    }

    private function createChecklistSteps(ServiceOrderChecklist $serviceOrderChecklist): void
    {
        $checklistSteps = $serviceOrderChecklist->checklist->checklistSteps;

        for ($i = 0; $i < $checklistSteps->count(); $i++) {
            /** @var \App\Models\ServiceOrderExecutionChecklistStep $serviceOrderExecutionChecklistStep */
            $serviceOrderExecutionChecklistStep = $this->serviceOrder->serviceOrderExecutionChecklistSteps()->create([
                'sequence' => $this->sequence,
                'checklist_id' => $checklistSteps[$i]->checklist_id,
                'checklist_name' => $serviceOrderChecklist->checklist->name,
                'checklist_step_name' => $checklistSteps[$i]->name,
                'data_type' => $checklistSteps[$i]->data_type,
                'boolean_table_positive_value_name' => $checklistSteps[$i]->boolean_table_positive_value_name,
                'boolean_table_negative_value_name' => $checklistSteps[$i]->boolean_table_negative_value_name,
                'requires_comment' => $checklistSteps[$i]->requires_comment,
                'is_checklist_final_step' => $checklistSteps->count() === $i + 1,
                'has_products' => $checklistSteps[$i]->has_products,
                'has_needs' => $checklistSteps[$i]->has_needs,
                'shows_checklist_in_quote' => $serviceOrderChecklist->checklist->shows_in_quote,
                'shows_in_quote' => $checklistSteps[$i]->shows_in_quote,
            ]);

            $this->sequence++;

            if (in_array($checklistSteps[$i]->data_type, [
                ChecklistStepDataTypeEnum::MultipleChoice->value,
                ChecklistStepDataTypeEnum::SingleChoice->value,
                ChecklistStepDataTypeEnum::BooleanTable->value,
                ChecklistStepDataTypeEnum::DescriptionTable->value,
            ])) {
                foreach ($checklistSteps[$i]->checklistStepOptions as $checklistStepOption) {
                    $serviceOrderExecutionChecklistStep->serviceOrderExecutionChecklistStepOptions()->create([
                        'sequence' => $checklistStepOption->sequence,
                        'value' => $checklistStepOption->value,
                        'requires_comment' => $checklistStepOption->requires_comment,
                    ]);
                }
            }
        }
    }
}
