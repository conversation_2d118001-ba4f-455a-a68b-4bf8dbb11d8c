<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Http\Integrations\ErpFlex\Requests\Sql\ErpFlexRunSqlQueryRequest;
use Carbon\Carbon;

class ErpFlexEquipmentService extends ErpFlexBaseService
{
    public function get(
        int $limit = 1000,
        int $offset = 0,
        ?Carbon $lastIntegrationAt = null,
        ?int $id = null,
        ?string $serialNumberField = null,
    ): array {
        $selectColumns = [
            'SB1_ID',
            'SB1_IDSBA',
            'SB1_Codigo',
            'SB1_Desc',
            'SB1_Caracteristicas',
        ];

        if ($serialNumberField) {
            $selectColumns[] = $serialNumberField;
        }

        if (!is_null($id)) {
            $wheres[] = "SB1_ID = $id";
        } else {
            $wheres = [
                "SB1_Tipo in ('IM', 'OU')",
                "AND SB1_AutomComercial = 'S'",
            ];
        }

        $response = $this->connector->send(
            new ErpFlexRunSqlQueryRequest($this->buildBaseGetSql('SB1', $selectColumns, $wheres, $limit, $offset, $lastIntegrationAt))
        );

        return $this->getQueryResultsFromApi(
            json_decode($response->body())
        );
    }

    public function getById(int $id, ?string $serialNumberField = null): ?object
    {
        $apiObjects = $this->get(1, 0, null, $id, $serialNumberField);

        return $apiObjects[0];
    }
}
