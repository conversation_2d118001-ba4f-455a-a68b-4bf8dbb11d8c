<?php

namespace App\Http\Integrations\ErpFlex\Services;

use App\Actions\ApiRequestLog\CreateApiRequestLog;
use App\Http\Integrations\ErpFlex\DataTransferObjects\Contract\ErpFlexCreateContractDto;
use App\Http\Integrations\ErpFlex\DataTransferObjects\Contract\ErpFlexUpdateContractDto;
use App\Http\Integrations\ErpFlex\Requests\Contract\ErpFlexCreateContractRequest;
use App\Http\Integrations\ErpFlex\Requests\Contract\ErpFlexUpdateContractRequest;
use App\Http\Integrations\ErpFlex\Requests\Sql\ErpFlexRunSqlQueryRequest;
use App\Models\Contract;
use App\Models\ThirdPartyContract;
use Carbon\Carbon;

class ErpFlexContractService extends ErpFlexBaseService
{
    public function get(int $limit = 1000, int $offset = 0, ?Carbon $lastIntegrationAt = null): array
    {
        $sql = "
            select
                SD5.SD5_ID,
                SD5.SD5_IDSA1,
                SD5.SD5_IDSD7,
                SD5.SD5_Doc,
                SD5.SD5_VigenciaDe,
                SD5.SD5_VigenciaAte,
                SD5.SD5_Assinatura,
                SD5.SD5_Tipo,
                SD6.SD6_IDSD5,
                SD6.SD6_IDSB1,
                SD6.SD6_IDSD7,
                SD6.SD6_Quant,
                SD6.SD6_QuantComercial,
                SD6.SD6_Vencto,
                SD6.SD6_Periodicidade,
                SD6.SD6_Intervalo,
                SD6.SD6_VigenciaDe,
                SD6.SD6_VigenciaAte
            from
                SD5 SD5
                join SD6 SD6 on SD5.SD5_ID = SD6.SD6_IDSD5
        ";

        if ($lastIntegrationAt) {
            $sql .= "WHERE (SD5_DT_INC >= '" . $lastIntegrationAt->format('Y-m-d H:i:s') . "' OR SD5_DT_ALT >= '" . $lastIntegrationAt->format('Y-m-d H:i:s') . '\') ';
        }

        $sql .= "limit $limit offset $offset";

        $response = $this->connector->send(
            new ErpFlexRunSqlQueryRequest($sql)
        );

        return $this->getQueryResultsFromApi(
            json_decode($response->body())
        );
    }

    public function getById(int $id): ?object
    {
        $response = $this->connector->send(
            new ErpFlexRunSqlQueryRequest($this->buildBaseGetSql('SD5', [], ["SD5_ID = $id"], 1))
        );

        $apiObjects = $this->getQueryResultsFromApi(
            json_decode($response->body())
        );

        return $apiObjects[0];
    }

    public function create(
        Contract $contract,
        ThirdPartyContract &$thirdPartyContract,
        ErpFlexCreateContractDto $erpFlexCreateContractDto,
    ): mixed {
        $request = new ErpFlexCreateContractRequest($erpFlexCreateContractDto);

        $response = $this->connector->send($request);

        $body = json_decode($response->body());

        if (is_null($body)) {
            CreateApiRequestLog::run(
                $contract,
                $response,
                false,
                'erp_flex_contract.create.empty_body',
                $request->defaultBody()
            );

            return null;
        }

        CreateApiRequestLog::run(
            model: $contract,
            response: $response,
            success: $body->success === 1,
            errorDescription: $body->success === 0
                ? $body->message
                : null,
            requestBody: $request->defaultBody()
        );

        $thirdPartyContract->update([
            'third_party_sent_data' => ouess_aes256cbc_encrypt(json_encode($request->defaultBody()))
        ]);

        return $body;
    }

    public function update(ErpFlexUpdateContractDto $erpFlexUpdateContractDto, Contract $contract): mixed
    {
        $request = new ErpFlexUpdateContractRequest($erpFlexUpdateContractDto);

        $response = $this->connector->send($request);

        $body = json_decode($response->body());

        if (is_null($body)) {
            CreateApiRequestLog::run(
                $contract,
                $response,
                false,
                'erp_flex_contract.update.empty_body',
                $request->defaultBody()
            );

            return null;
        }

        CreateApiRequestLog::run(
            model: $contract,
            response: $response,
            success: $body->success === 1,
            errorDescription: $body->success === 0
                ? $body->message
                : null,
            requestBody: $request->defaultBody()
        );

        return $body;
    }
}
