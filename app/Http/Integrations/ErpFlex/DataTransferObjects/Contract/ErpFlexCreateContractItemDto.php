<?php

namespace App\Http\Integrations\ErpFlex\DataTransferObjects\Contract;

use App\Http\Integrations\ErpFlex\DataTransferObjects\ErpFlexArrayable;

class ErpFlexCreateContractItemDto implements ErpFlexArrayable
{
    public function __construct(
        private int $id_produto,
        private int $id_vendedor,
        private float $precuni,
        private float $prcuniexcedente,
        private float $prcunicomercial,
        private float $quantidade,
        private float $quantidade_comercial,
        private float $valitem,
        private int $vencto,
        private int $periodicidade,
        private int $intervalo,
        private int $parcelas,
        private int $sittribicms,
        private int $sittribipi,
        private string $sittribpis,
        private string $sittribcofins,
        private int $id_natureza,
        private int $status_item,
        private string $assinatura,
        private string $vigenciade,
        private string $faturamento,
        private int $id_cfop,
    ) {}

    public function toArray(): array
    {
        return [
            'id_produto' => $this->id_produto,
            'id_vendedor' => $this->id_vendedor,
            'precuni' => $this->precuni,
            'prcuniexcedente' => $this->prcuniexcedente,
            'prcunicomercial' => $this->prcunicomercial,
            'quantidade' => $this->quantidade,
            'quantidade_comercial' => $this->quantidade_comercial,
            'valitem' => $this->valitem,
            'vencto' => $this->vencto,
            'periodicidade' => $this->periodicidade,
            'intervalo' => $this->intervalo,
            'parcelas' => $this->parcelas,
            'sittribicms' => $this->sittribicms,
            'sittribipi' => $this->sittribipi,
            'sittribpis' => $this->sittribpis,
            'sittribcofins' => $this->sittribcofins,
            'id_natureza' => $this->id_natureza,
            'status_item' => $this->status_item,
            'assinatura' => $this->assinatura,
            'vigenciade' => $this->vigenciade,
            'faturamento' => $this->faturamento,
            'id_cfop' => $this->id_cfop,
        ];
    }
}
