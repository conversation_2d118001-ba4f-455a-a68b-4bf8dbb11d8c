<?php

namespace App\Policies;

use App\Enums\ServiceOrderStatusEnum;
use App\Models\Permission;
use App\Models\ServiceOrder;
use App\Models\User;

class ServiceOrderPolicy
{
    public function viewAny(User $user): bool
    {
        return !is_null($user->customer)
            || $user->hasPermissionTo(Permission::GET_SERVICE_ORDERS);
    }

    public function view(User $user, ServiceOrder $serviceOrder): bool
    {
        return !is_null($user->customer)
            || $user->hasPermissionTo(Permission::GET_SERVICE_ORDERS);
    }

    public function create(User $user): bool
    {
        return $user->hasPermissionTo(Permission::CREATE_SERVICE_ORDERS);
    }

    public function update(User $user, ServiceOrder $serviceOrder): bool
    {
        return $user->hasPermissionTo(Permission::UPDATE_SERVICE_ORDERS)
            && in_array($serviceOrder->status, [ServiceOrderStatusEnum::Pending->value, ServiceOrderStatusEnum::Scheduled->value]);
    }

    public function delete(User $user, ServiceOrder $serviceOrder): bool
    {
        return $user->hasPermissionTo(Permission::DELETE_SERVICE_ORDERS)
            && in_array($serviceOrder->status, [ServiceOrderStatusEnum::Pending->value, ServiceOrderStatusEnum::Scheduled->value]);
    }

    public function restore(User $user, ServiceOrder $serviceOrder): bool
    {
        return false;
    }

    public function forceDelete(User $user, ServiceOrder $serviceOrder): bool
    {
        return false;
    }
}
